const fs = require('fs').promises;
const path = require('path');
const supabase = require('../supabaseClient');

async function createEssayDataset() {
  try {
    // Read the CSV file
    const csvPath = path.join(__dirname, '../data/dataset_essay.csv');
    const csvContent = await fs.readFile(csvPath, 'utf8');

    // Create dataset entry
    const datasetToInsert = {
      name: 'Essay Scoring Dataset',
      description: 'Dataset for Essay scoring evaluation with competency analysis (Innovation, Accountability, Leadership, Problem Solving)',
      data: {
        csvData: csvContent,
        type: 'essay_scoring'
      },
      label: 'essay',
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    };

    const { data: newDataset, error: insertError } = await supabase
      .from('datasets')
      .insert([datasetToInsert])
      .select('*')
      .single();

    if (insertError) {
      console.error('Error inserting Essay dataset:', insertError);
      return;
    }

    console.log('Essay dataset created successfully:', newDataset);
    console.log('Dataset ID:', newDataset.id);
    console.log('Dataset name:', newDataset.name);
    console.log('Dataset label:', newDataset.label);

  } catch (error) {
    console.error('Error creating Essay dataset:', error);
  }
}

// Run the script if called directly
if (require.main === module) {
  createEssayDataset()
    .then(() => {
      console.log('Script completed');
      process.exit(0);
    })
    .catch((error) => {
      console.error('Script failed:', error);
      process.exit(1);
    });
}

module.exports = createEssayDataset;
