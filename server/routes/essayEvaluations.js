const express = require('express');
const router = express.Router();
const essayGeminiService = require('../services/essayGemini');
const supabase = require('../supabaseClient');
const Papa = require('papaparse');

// Background processing function
async function processEssayEvaluationAsync(evaluationId, datasetRows, promptContent) {
  try {
    console.log(`Starting background processing for Essay evaluation ${evaluationId}`);

    // Run the Essay evaluation
    const result = await essayGeminiService.runEssayEvaluation(datasetRows, promptContent);

    // Update the record with the result
    const { error: updateError } = await supabase
      .from('essay_evaluations')
      .update({
        output: result,
        details: result,
        status: 'completed',
        innovation_accuracy: result.summary.innovationAccuracy,
        accountability_accuracy: result.summary.accountabilityAccuracy,
        leadership_accuracy: result.summary.leadershipAccuracy,
        problem_solving_accuracy: result.summary.problemSolvingAccuracy,
        total_rows_processed: result.summary.totalRows
      })
      .eq('id', evaluationId);

    if (updateError) {
      console.error('Error updating Essay evaluation:', updateError);
      
      // Update status to error
      await supabase
        .from('essay_evaluations')
        .update({
          status: 'error',
          output: { error: 'Failed to save evaluation results' }
        })
        .eq('id', evaluationId);
    } else {
      console.log(`Essay evaluation ${evaluationId} completed successfully`);
    }

  } catch (error) {
    console.error(`Error in background processing for Essay evaluation ${evaluationId}:`, error);
    
    // Update status to error
    await supabase
      .from('essay_evaluations')
      .update({
        status: 'error',
        output: { error: error.message }
      })
      .eq('id', evaluationId);
  }
}

// GET all Essay evaluations
router.get('/', async (req, res) => {
  try {
    const { data: evaluations, error } = await supabase
      .from('essay_evaluations')
      .select('*')
      .order('timestamp', { ascending: false });

    if (error) {
      console.error('Error fetching Essay evaluations:', error);
      return res.status(500).json({ error: 'Failed to fetch Essay evaluations' });
    }

    res.json(evaluations);
  } catch (error) {
    console.error('Error in GET /essay-evaluations:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

// GET Essay evaluation status by ID
router.get('/:id/status', async (req, res) => {
  try {
    const { id } = req.params;

    const { data: evaluation, error } = await supabase
      .from('essay_evaluations')
      .select('*')
      .eq('id', id)
      .single();

    if (error) {
      console.error('Error fetching Essay evaluation status:', error);
      return res.status(500).json({ error: 'Failed to fetch Essay evaluation status' });
    }

    if (!evaluation) {
      return res.status(404).json({ error: 'Essay evaluation not found' });
    }

    res.json(evaluation);
  } catch (error) {
    console.error('Error in GET /essay-evaluations/:id/status:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

// POST run new Essay evaluation
router.post('/run', async (req, res) => {
  try {
    const { datasetId } = req.body;

    if (!datasetId) {
      return res.status(400).json({ error: 'Dataset ID is required' });
    }

    // Fetch the dataset
    const { data: dataset, error: datasetError } = await supabase
      .from('datasets')
      .select('*')
      .eq('id', datasetId)
      .single();

    if (datasetError || !dataset) {
      console.error('Error fetching dataset:', datasetError);
      return res.status(400).json({ error: 'Dataset not found' });
    }

    // Validate that the dataset is labeled for Essay evaluation
    if (dataset.label !== 'essay') {
      return res.status(400).json({
        error: 'Invalid dataset type. Please select a dataset labeled for Essay evaluation.'
      });
    }

    // Parse CSV data from dataset
    let datasetRows;
    try {
      if (dataset.data && dataset.data.csvData) {
        // Parse CSV data
        const parseResult = Papa.parse(dataset.data.csvData, {
          header: true,
          skipEmptyLines: true
        });
        datasetRows = parseResult.data;
      } else {
        return res.status(400).json({ error: 'Dataset does not contain valid CSV data' });
      }
    } catch (parseError) {
      console.error('Error parsing dataset CSV:', parseError);
      return res.status(400).json({ error: 'Failed to parse dataset CSV data' });
    }

    if (!datasetRows || datasetRows.length === 0) {
      return res.status(400).json({ error: 'Dataset is empty or invalid' });
    }

    // Validate dataset structure (should have input_text and expected_results columns)
    const firstRow = datasetRows[0];
    if (!firstRow.input_text || !firstRow.expected_results) {
      return res.status(400).json({ 
        error: 'Dataset must have "input_text" and "expected_results" columns' 
      });
    }

    // Get Essay prompt from database
    const { data: prompts, error: promptsError } = await supabase
      .from('prompts')
      .select('*')
      .eq('id', 17) // Essay Scoring prompt
      .single();

    if (promptsError || !prompts) {
      console.error('Error fetching Essay prompt:', promptsError);
      return res.status(500).json({ error: 'Failed to load Essay prompt from database' });
    }

    const promptContent = prompts.content;

    // Create initial record with "in_progress" status
    const evaluationToInsert = {
      dataset_id: datasetId,
      dataset_name: dataset.name,
      output: null,
      status: 'in_progress',
      prompt_version: prompts.version, // Database prompt version
      prompt_content: promptContent,
      timestamp: new Date().toISOString(),
      details: null,
      innovation_accuracy: null,
      accountability_accuracy: null,
      leadership_accuracy: null,
      problem_solving_accuracy: null,
      total_rows_processed: 0
    };

    const { data: newEvaluation, error: insertError } = await supabase
      .from('essay_evaluations')
      .insert([evaluationToInsert])
      .select('*')
      .single();

    if (insertError) {
      console.error('Error inserting Essay evaluation:', insertError);
      return res.status(500).json({ error: 'Failed to save Essay evaluation' });
    }

    // Start background processing
    processEssayEvaluationAsync(newEvaluation.id, datasetRows, promptContent);

    res.json(newEvaluation);
  } catch (error) {
    console.error('Error in POST /essay-evaluations/run:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

module.exports = router;
