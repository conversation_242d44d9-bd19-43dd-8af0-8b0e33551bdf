import axios from 'axios';

const API_BASE_URL = '/api';

const api = axios.create({
  baseURL: API_BASE_URL,
  headers: {
    'Content-Type': 'application/json',
  },
});

const plainApi = axios.create({
  baseURL: API_BASE_URL
});

export const promptsApi = {
  getAll: () => api.get('/prompts'),
  getById: (id) => api.get(`/prompts/${id}`),
  getVersion: (id, version) => api.get(`/prompts/${id}/versions/${version}`),
  update: (id, content) => api.put(`/prompts/${id}`, { content }),
};

export const evaluationsApi = {
  getAll: () => api.get('/evaluations'),
  run: (input) => api.post('/evaluations/run', { input }),
  updateAnnotation: (id, annotation) => api.put(`/evaluations/${id}/annotation`, { annotation }),
};

export const lgdEvaluationsApi = {
  getAll: () => api.get('/lgd-evaluations'),
  run: (input) => api.post('/lgd-evaluations/run', { input }),
  getStatus: (id) => api.get(`/lgd-evaluations/${id}/status`),
  updateAnnotation: (id, annotation) => api.put(`/lgd-evaluations/${id}/annotation`, { annotation }),
};

export const beiEvaluationsApi = {
  getAll: () => api.get('/bei-evaluations'),
  run: (input) => api.post('/bei-evaluations/run', { input }),
  getStatus: (id) => api.get(`/bei-evaluations/${id}/status`),
  updateAnnotation: (id, annotation) => api.put(`/bei-evaluations/${id}/annotation`, { annotation }),
};

export const etrayEvaluationsApi = {
  getAll: () => api.get('/etray-evaluations'),
  run: (formData) => api.post('/etray-evaluations/run', formData, {
    headers: {
      'Content-Type': 'multipart/form-data',
    },
  }),
  getStatus: (id) => api.get(`/etray-evaluations/${id}/status`),
  updateAnnotation: (id, annotation) => api.put(`/etray-evaluations/${id}/annotation`, { annotation }),
};

export const aiInterviewEvaluationsApi = {
  getAll: () => api.get('/ai-interview-evaluations'),
  run: (datasetId) => api.post('/ai-interview-evaluations/run', { datasetId }),
  getStatus: (id) => api.get(`/ai-interview-evaluations/${id}/status`),
  updateAnnotation: (id, annotation) => api.put(`/ai-interview-evaluations/${id}/annotation`, { annotation }),
};

export const aiInterviewV2EvaluationsApi = {
  getAll: () => api.get('/ai-interview-v2-evaluations'),
  run: (datasetId, transcribeModel, transcribeTemperature, analysisModel, analysisTemperature) =>
    api.post('/ai-interview-v2-evaluations/run', {
      datasetId,
      transcribeModel,
      transcribeTemperature,
      analysisModel,
      analysisTemperature
    }),
  getStatus: (id) => api.get(`/ai-interview-v2-evaluations/${id}/status`),
  updateAnnotation: (id, annotation) => api.put(`/ai-interview-v2-evaluations/${id}/annotation`, { annotation }),
};

export const englishProficiencyEvaluationsApi = {
  getAll: () => api.get('/english-proficiency-evaluations'),
  run: (datasetId, transcribeModel, transcribeTemperature, analysisModel, analysisTemperature) =>
    api.post('/english-proficiency-evaluations/run', {
      datasetId,
      transcribeModel,
      transcribeTemperature,
      analysisModel,
      analysisTemperature
    }),
  getStatus: (id) => api.get(`/english-proficiency-evaluations/${id}/status`),
  updateAnnotation: (id, annotation) => api.put(`/english-proficiency-evaluations/${id}/annotation`, { annotation }),
};

export const loiEvaluationsApi = {
  getAll: () => api.get('/loi-evaluations'),
  run: (datasetId) => api.post('/loi-evaluations/run', { datasetId }),
  getStatus: (id) => api.get(`/loi-evaluations/${id}/status`),
};

export const essayEvaluationsApi = {
  getAll: () => api.get('/essay-evaluations'),
  run: (datasetId) => api.post('/essay-evaluations/run', { datasetId }),
  getStatus: (id) => api.get(`/essay-evaluations/${id}/status`),
};

export const datasetsApi = {
  getAll: (label) => api.get('/datasets', { params: label ? { label } : {} }),
  getById: (id) => api.get(`/datasets/${id}`),
  create: (dataset) => api.post('/datasets', dataset),
  update: (id, dataset) => api.put(`/datasets/${id}`, dataset),
  delete: (id) => api.delete(`/datasets/${id}`),
};

export const dataApi = {
  getSampleTranscript: () => plainApi.get('/data/lgd_transcript.txt'),
  getSampleCompetencies: () => plainApi.get('/data/lgd_competency_guidelines.txt'),
  getBEITranscript: () => plainApi.get('/data/bei_transcript.txt'),
  getEtrayTranscript: () => plainApi.get('/data/etray_transcript.txt')
};

export default api;
