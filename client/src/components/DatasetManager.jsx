import React, { useState, useEffect } from 'react';
import { datasetsApi } from '../services/api';

const DatasetManager = ({ onDatasetSelect, selectedDatasetId, evaluationType }) => {
  const [datasets, setDatasets] = useState([]);
  const [loading, setLoading] = useState(true);
  const [showCreateForm, setShowCreateForm] = useState(false);
  const [editingDataset, setEditingDataset] = useState(null);
  const [formData, setFormData] = useState({
    name: '',
    description: '',
    data: { data: [{ question: '', answer: '', answer_url: '' }] },
    label: 'ai_interview'
  });

  // Helper function to get dataset item count based on dataset type
  const getDatasetItemCount = (dataset) => {
    if (!dataset || !dataset.data) return 0;

    // For LoI datasets with csvData format
    if (dataset.data.type === 'loi_scoring' && dataset.data.csvData) {
      try {
        // Parse CSV properly to count actual rows
        // Split by lines and reconstruct CSV rows by tracking quotes
        const csvData = dataset.data.csvData;
        let rowCount = 0;
        let inQuotes = false;
        let currentRow = '';

        for (let i = 0; i < csvData.length; i++) {
          const char = csvData[i];
          currentRow += char;

          if (char === '"') {
            // Toggle quote state, but handle escaped quotes
            if (i + 1 < csvData.length && csvData[i + 1] === '"') {
              // Skip escaped quote
              i++;
              currentRow += '"';
            } else {
              inQuotes = !inQuotes;
            }
          } else if (char === '\n' && !inQuotes) {
            // End of row when we hit newline outside of quotes
            if (currentRow.trim()) {
              rowCount++;
            }
            currentRow = '';
          }
        }

        // Handle last row if it doesn't end with newline
        if (currentRow.trim()) {
          rowCount++;
        }

        // Subtract 1 for header row
        return Math.max(0, rowCount - 1);
      } catch (error) {
        console.error('Error parsing CSV data:', error);
        // Fallback to simple line count
        const lines = dataset.data.csvData.split('\n').filter(line => line.trim());
        return Math.max(0, lines.length - 1);
      }
    }

    // For AI Interview datasets with data array format
    if (dataset.data.data && Array.isArray(dataset.data.data)) {
      return dataset.data.data.length;
    }

    return 0;
  };

  useEffect(() => {
    loadDatasets();
  }, [evaluationType]);

  const loadDatasets = async () => {
    try {
      setLoading(true);
      // Map evaluation types to dataset labels
      const labelMap = {
        'ai-interview': 'ai_interview',
        'ai-interview-v2': 'ai_interview',
        'english-proficiency': 'ai_interview',
        'loi': 'loi'
      };

      const label = labelMap[evaluationType];
      const response = await datasetsApi.getAll(label);
      setDatasets(response.data);
    } catch (error) {
      console.error('Error loading datasets:', error);
      alert('Failed to load datasets');
    } finally {
      setLoading(false);
    }
  };

  const handleCreateDataset = () => {
    // Map evaluation types to dataset labels
    const labelMap = {
      'ai-interview': 'ai_interview',
      'ai-interview-v2': 'ai_interview',
      'english-proficiency': 'ai_interview',
      'loi': 'loi',
      'essay': 'essay'
    };

    setFormData({
      name: '',
      description: '',
      data: { data: [{ question: '', answer: '', answer_url: '' }] },
      label: labelMap[evaluationType] || 'ai_interview'
    });
    setEditingDataset(null);
    setShowCreateForm(true);
  };

  const handleEditDataset = (dataset) => {
    setFormData({
      name: dataset.name,
      description: dataset.description || '',
      data: dataset.data,
      label: dataset.label || 'ai_interview'
    });
    setEditingDataset(dataset);
    setShowCreateForm(true);
  };

  const handleDeleteDataset = async (datasetId) => {
    if (!confirm('Are you sure you want to delete this dataset?')) {
      return;
    }

    try {
      await datasetsApi.delete(datasetId);
      await loadDatasets();
      if (selectedDatasetId === datasetId) {
        onDatasetSelect(null);
      }
    } catch (error) {
      console.error('Error deleting dataset:', error);
      alert('Failed to delete dataset');
    }
  };

  const handleSaveDataset = async () => {
    if (!formData.name.trim()) {
      alert('Dataset name is required');
      return;
    }

    if (!formData.data.data || formData.data.data.length === 0) {
      alert('At least one question-answer pair is required');
      return;
    }

    // Validate all question-answer pairs
    for (const item of formData.data.data) {
      if (!item.question.trim() || !item.answer_url.trim()) {
        alert('All question and answer_url fields must be filled');
        return;
      }
    }

    try {
      if (editingDataset) {
        await datasetsApi.update(editingDataset.id, formData);
      } else {
        await datasetsApi.create(formData);
      }
      
      await loadDatasets();
      setShowCreateForm(false);
      setEditingDataset(null);
    } catch (error) {
      console.error('Error saving dataset:', error);
      alert('Failed to save dataset');
    }
  };

  const handleQuestionAnswerChange = (index, field, value) => {
    const newData = { ...formData };
    newData.data.data[index][field] = value;
    setFormData(newData);
  };

  const addQuestionAnswerPair = () => {
    const newData = { ...formData };
    newData.data.data.push({ question: '', answer: '', answer_url: '' });
    setFormData(newData);
  };

  const removeQuestionAnswerPair = (index) => {
    const newData = { ...formData };
    newData.data.data.splice(index, 1);
    setFormData(newData);
  };

  if (loading) {
    return <div>Loading datasets...</div>;
  }

  return (
    <div style={{
      border: '1px solid #ddd',
      borderRadius: '8px',
      padding: '16px',
      backgroundColor: 'white',
      marginBottom: '20px'
    }}>
      <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '16px' }}>
        <h3 style={{ margin: 0 }}>Dataset Management</h3>
        {evaluationType !== 'loi' && (
          <button
            onClick={handleCreateDataset}
            style={{
              padding: '8px 16px',
              border: 'none',
              borderRadius: '4px',
              backgroundColor: '#007bff',
              color: 'white',
              cursor: 'pointer'
            }}
          >
            Create New Dataset
          </button>
        )}
      </div>

      {datasets.length === 0 ? (
        <p style={{ color: '#666', fontStyle: 'italic' }}>No datasets available. Create one to get started.</p>
      ) : (
        <div style={{ marginBottom: '16px' }}>
          <label style={{ display: 'block', marginBottom: '8px', fontWeight: 'bold' }}>
            Select Dataset for Evaluation:
          </label>
          <select
            value={selectedDatasetId || ''}
            onChange={(e) => onDatasetSelect(e.target.value ? parseInt(e.target.value) : null)}
            style={{
              width: '100%',
              padding: '8px',
              border: '1px solid #ccc',
              borderRadius: '4px',
              fontSize: '14px'
            }}
          >
            <option value="">-- Select a dataset --</option>
            {datasets.map(dataset => (
              <option key={dataset.id} value={dataset.id}>
                {dataset.name} ({getDatasetItemCount(dataset)} items)
              </option>
            ))}
          </select>
        </div>
      )}

      <div style={{ marginTop: '16px' }}>
        <h4>Available Datasets:</h4>
        {datasets.map(dataset => (
          <div key={dataset.id} style={{
            border: '1px solid #eee',
            borderRadius: '4px',
            padding: '12px',
            marginBottom: '8px',
            backgroundColor: selectedDatasetId === dataset.id ? '#e3f2fd' : '#f8f9fa'
          }}>
            <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
              <div>
                <strong>{dataset.name}</strong>
                {dataset.description && (
                  <p style={{ margin: '4px 0', color: '#666', fontSize: '14px' }}>
                    {dataset.description}
                  </p>
                )}
                <p style={{ margin: '4px 0', fontSize: '12px', color: '#888' }}>
                  {getDatasetItemCount(dataset)} items • Created: {new Date(dataset.createdAt).toLocaleDateString()}
                </p>
              </div>
              <div style={{ display: 'flex', gap: '8px' }}>
                {evaluationType !== 'loi' && (
                  <button
                    onClick={() => handleEditDataset(dataset)}
                    style={{
                      padding: '4px 8px',
                      border: '1px solid #007bff',
                      borderRadius: '4px',
                      backgroundColor: 'white',
                      color: '#007bff',
                      cursor: 'pointer',
                      fontSize: '12px'
                    }}
                  >
                    Edit
                  </button>
                )}
                <button
                  onClick={() => handleDeleteDataset(dataset.id)}
                  style={{
                    padding: '4px 8px',
                    border: '1px solid #dc3545',
                    borderRadius: '4px',
                    backgroundColor: 'white',
                    color: '#dc3545',
                    cursor: 'pointer',
                    fontSize: '12px'
                  }}
                >
                  Delete
                </button>
              </div>
            </div>
          </div>
        ))}
      </div>

      {/* Create/Edit Dataset Modal */}
      {showCreateForm && (
        <div style={{
          position: 'fixed',
          top: 0,
          left: 0,
          right: 0,
          bottom: 0,
          backgroundColor: 'rgba(0, 0, 0, 0.5)',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          zIndex: 10000
        }} onClick={() => setShowCreateForm(false)}>
          <div style={{
            backgroundColor: 'white',
            borderRadius: '8px',
            padding: '20px',
            maxWidth: '80%',
            maxHeight: '80%',
            overflow: 'auto',
            width: '600px'
          }} onClick={(e) => e.stopPropagation()}>
            <h3>{editingDataset ? 'Edit Dataset' : 'Create New Dataset'}</h3>
            
            <div style={{ marginBottom: '16px' }}>
              <label style={{ display: 'block', marginBottom: '8px', fontWeight: 'bold' }}>
                Dataset Name:
              </label>
              <input
                type="text"
                value={formData.name}
                onChange={(e) => setFormData({ ...formData, name: e.target.value })}
                style={{
                  width: '100%',
                  padding: '8px',
                  border: '1px solid #ccc',
                  borderRadius: '4px'
                }}
              />
            </div>

            <div style={{ marginBottom: '16px' }}>
              <label style={{ display: 'block', marginBottom: '8px', fontWeight: 'bold' }}>
                Description (optional):
              </label>
              <textarea
                value={formData.description}
                onChange={(e) => setFormData({ ...formData, description: e.target.value })}
                style={{
                  width: '100%',
                  padding: '8px',
                  border: '1px solid #ccc',
                  borderRadius: '4px',
                  minHeight: '60px'
                }}
              />
            </div>

            <div style={{ marginBottom: '16px' }}>
              <label style={{ display: 'block', marginBottom: '8px', fontWeight: 'bold' }}>
                Question-Answer Pairs:
              </label>
              {formData.data.data.map((item, index) => (
                <div key={index} style={{
                  border: '1px solid #eee',
                  borderRadius: '4px',
                  padding: '12px',
                  marginBottom: '8px'
                }}>
                  <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '8px' }}>
                    <strong>Question {index + 1}</strong>
                    {formData.data.data.length > 1 && (
                      <button
                        onClick={() => removeQuestionAnswerPair(index)}
                        style={{
                          padding: '2px 6px',
                          border: '1px solid #dc3545',
                          borderRadius: '4px',
                          backgroundColor: 'white',
                          color: '#dc3545',
                          cursor: 'pointer',
                          fontSize: '12px'
                        }}
                      >
                        Remove
                      </button>
                    )}
                  </div>
                  
                  <div style={{ marginBottom: '8px' }}>
                    <label style={{ display: 'block', marginBottom: '4px', fontSize: '14px' }}>Question:</label>
                    <input
                      type="text"
                      value={item.question}
                      onChange={(e) => handleQuestionAnswerChange(index, 'question', e.target.value)}
                      style={{
                        width: '100%',
                        padding: '6px',
                        border: '1px solid #ccc',
                        borderRadius: '4px',
                        fontSize: '14px'
                      }}
                    />
                  </div>
                  
                  {/* Only show answer field for non-English Proficiency evaluations */}
                  {evaluationType !== 'english-proficiency' && (
                    <div style={{ marginBottom: '8px' }}>
                      <label style={{ display: 'block', marginBottom: '4px', fontSize: '14px' }}>Answer (optional):</label>
                      <textarea
                        value={item.answer}
                        onChange={(e) => handleQuestionAnswerChange(index, 'answer', e.target.value)}
                        style={{
                          width: '100%',
                          padding: '6px',
                          border: '1px solid #ccc',
                          borderRadius: '4px',
                          fontSize: '14px',
                          minHeight: '60px'
                        }}
                      />
                    </div>
                  )}
                  
                  <div>
                    <label style={{ display: 'block', marginBottom: '4px', fontSize: '14px' }}>Answer URL:</label>
                    <input
                      type="text"
                      value={item.answer_url || ''}
                      onChange={(e) => handleQuestionAnswerChange(index, 'answer_url', e.target.value)}
                      style={{
                        width: '100%',
                        padding: '6px',
                        border: '1px solid #ccc',
                        borderRadius: '4px',
                        fontSize: '14px'
                      }}
                    />
                  </div>
                </div>
              ))}
              
              <button
                onClick={addQuestionAnswerPair}
                style={{
                  padding: '8px 16px',
                  border: '1px solid #28a745',
                  borderRadius: '4px',
                  backgroundColor: 'white',
                  color: '#28a745',
                  cursor: 'pointer',
                  fontSize: '14px'
                }}
              >
                Add Question-Answer Pair
              </button>
            </div>

            <div style={{ display: 'flex', gap: '10px', justifyContent: 'flex-end' }}>
              <button
                onClick={() => setShowCreateForm(false)}
                style={{
                  padding: '8px 16px',
                  border: '1px solid #6c757d',
                  borderRadius: '4px',
                  backgroundColor: 'white',
                  color: '#6c757d',
                  cursor: 'pointer'
                }}
              >
                Cancel
              </button>
              <button
                onClick={handleSaveDataset}
                style={{
                  padding: '8px 16px',
                  border: 'none',
                  borderRadius: '4px',
                  backgroundColor: '#28a745',
                  color: 'white',
                  cursor: 'pointer'
                }}
              >
                {editingDataset ? 'Update Dataset' : 'Create Dataset'}
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default DatasetManager;
