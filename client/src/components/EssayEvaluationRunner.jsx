import React, { useState, useEffect, useRef } from 'react';
import { essayEvaluationsApi } from '../services/api';

const EssayEvaluationRunner = ({ selectedDatasetId, onRun, onPollingUpdate }) => {
  const [running, setRunning] = useState(false);
  const [lastResult, setLastResult] = useState(null);
  const [pollingEvaluations, setPollingEvaluations] = useState(new Set());

  const pollingIntervals = useRef(new Map());

  // Cleanup polling intervals on unmount
  useEffect(() => {
    return () => {
      pollingIntervals.current.forEach(intervalId => clearInterval(intervalId));
      pollingIntervals.current.clear();
    };
  }, []);

  const startPolling = (evaluationId) => {
    if (pollingIntervals.current.has(evaluationId)) {
      return; // Already polling this evaluation
    }

    setPollingEvaluations(prev => new Set([...prev, evaluationId]));

    const intervalId = setInterval(async () => {
      try {
        const response = await essayEvaluationsApi.getStatus(evaluationId);
        
        if (response.data.status === 'completed' || response.data.status === 'error') {
          // Stop polling for this evaluation
          clearInterval(intervalId);
          pollingIntervals.current.delete(evaluationId);
          setPollingEvaluations(prev => {
            const newSet = new Set(prev);
            newSet.delete(evaluationId);
            return newSet;
          });

          // Notify parent component about the update
          if (onPollingUpdate) {
            onPollingUpdate(response.data);
          }

          console.log(`Essay evaluation ${evaluationId} ${response.data.status}`);
        } else {
          // Update the evaluation data even if still in progress
          if (onPollingUpdate) {
            onPollingUpdate(response.data);
          }
        }
      } catch (error) {
        console.error('Error polling evaluation status:', error);
        // Continue polling even if there's an error
      }
    }, 30000); // Poll every 30 seconds

    pollingIntervals.current.set(evaluationId, intervalId);
  };

  const handleRun = async () => {
    if (!selectedDatasetId) {
      alert('Please select a dataset first');
      return;
    }

    try {
      setRunning(true);
      const result = await onRun(selectedDatasetId);
      setLastResult(result);

      // Start polling if the result has "in_progress" status
      if (result && result.status === "in_progress") {
        startPolling(result.id);
      }
    } catch (error) {
      alert('Failed to run Essay evaluation');
    } finally {
      setRunning(false);
    }
  };

  const getPollingStatus = () => {
    if (pollingEvaluations.size === 0) return null;
    
    return (
      <div style={{
        padding: '12px',
        backgroundColor: '#e3f2fd',
        border: '1px solid #2196f3',
        borderRadius: '4px',
        color: '#1976d2',
        marginBottom: '16px'
      }}>
        <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
          <div style={{
            width: '16px',
            height: '16px',
            border: '2px solid #1976d2',
            borderTop: '2px solid transparent',
            borderRadius: '50%',
            animation: 'spin 1s linear infinite'
          }}></div>
          <span>
            {pollingEvaluations.size === 1 
              ? `Monitoring 1 evaluation in progress...`
              : `Monitoring ${pollingEvaluations.size} evaluations in progress...`
            }
          </span>
        </div>
        <style>
          {`
            @keyframes spin {
              0% { transform: rotate(0deg); }
              100% { transform: rotate(360deg); }
            }
          `}
        </style>
      </div>
    );
  };

  return (
    <div style={{
      border: '1px solid #ddd',
      borderRadius: '8px',
      padding: '16px',
      backgroundColor: 'white'
    }}>
      <h3 style={{ marginTop: 0 }}>Run Essay Scoring Evaluation</h3>

      {!selectedDatasetId && (
        <div style={{
          padding: '12px',
          backgroundColor: '#fff3cd',
          border: '1px solid #ffeaa7',
          borderRadius: '4px',
          color: '#856404',
          marginBottom: '16px'
        }}>
          Please select a dataset from the Dataset Management section above to run an evaluation.
        </div>
      )}

      <div style={{ marginBottom: '16px' }}>
        <p style={{ margin: '0 0 8px 0', color: '#666' }}>
          This evaluation will:
        </p>
        <ol style={{ margin: '0 0 0 20px', color: '#666' }}>
          <li>Process each row in the dataset with the Essay prompt</li>
          <li>Compare Gemini responses with expected competency scores</li>
          <li>Calculate accuracy for all four competencies: "Innovation", "Accountability", "Leadership", and "Problem Solving"</li>
          <li>Provide detailed results showing individual row comparisons</li>
        </ol>
      </div>

      {getPollingStatus()}

      <button
        onClick={handleRun}
        disabled={running || !selectedDatasetId}
        style={{
          padding: '12px 24px',
          backgroundColor: running || !selectedDatasetId ? '#ccc' : '#007bff',
          color: 'white',
          border: 'none',
          borderRadius: '4px',
          cursor: running || !selectedDatasetId ? 'not-allowed' : 'pointer',
          fontSize: '16px',
          fontWeight: 'bold'
        }}
      >
        {running ? 'Running Essay Evaluation...' : 'Run Essay Evaluation'}
      </button>

      {lastResult && (
        <div style={{
          marginTop: '16px',
          padding: '12px',
          backgroundColor: '#f8f9fa',
          border: '1px solid #dee2e6',
          borderRadius: '4px'
        }}>
          <h4 style={{ margin: '0 0 8px 0' }}>Last Run Result:</h4>
          <p style={{ margin: '0', color: '#666' }}>
            Status: <strong>{lastResult.status}</strong>
          </p>
          <p style={{ margin: '4px 0 0 0', color: '#666' }}>
            Dataset: <strong>{lastResult.dataset_name}</strong>
          </p>
          <p style={{ margin: '4px 0 0 0', color: '#666' }}>
            Started: <strong>{new Date(lastResult.timestamp).toLocaleString()}</strong>
          </p>
        </div>
      )}
    </div>
  );
};

export default EssayEvaluationRunner;
