import React, { useState } from 'react';

// Helper function to get color based on difference
const getDifferenceColor = (diff) => {
  if (diff === 0) return '#28a745'; // Green for exact match
  if (diff === 1 || diff === -1) return '#ffc107'; // Yellow for close
  return '#dc3545'; // Red for far off
};

// Helper function to get difference text
const getDifferenceText = (diff) => {
  if (diff === 0) return '✓';
  if (diff > 0) return `+${diff}`;
  return `${diff}`;
};

// Modal component for displaying input text
const InputModal = ({ isOpen, inputText, onClose }) => {
  if (!isOpen) return null;

  return (
    <div style={{
      position: 'fixed',
      top: 0,
      left: 0,
      right: 0,
      bottom: 0,
      backgroundColor: 'rgba(0, 0, 0, 0.5)',
      display: 'flex',
      alignItems: 'center',
      justifyContent: 'center',
      zIndex: 1000
    }}>
      <div style={{
        backgroundColor: 'white',
        padding: '20px',
        borderRadius: '8px',
        maxWidth: '80%',
        maxHeight: '80%',
        overflow: 'auto',
        position: 'relative'
      }}>
        <button
          onClick={onClose}
          style={{
            position: 'absolute',
            top: '10px',
            right: '10px',
            background: 'none',
            border: 'none',
            fontSize: '20px',
            cursor: 'pointer',
            color: '#666'
          }}
        >
          ×
        </button>
        <h3 style={{ marginTop: 0, marginBottom: '16px' }}>Input Text</h3>
        <div style={{
          whiteSpace: 'pre-wrap',
          lineHeight: '1.5',
          color: '#333',
          fontSize: '14px'
        }}>
          {inputText}
        </div>
      </div>
    </div>
  );
};

// Modal component for displaying evidences
const EvidenceModal = ({ isOpen, competency, score, allLevelEvidences, onClose }) => {
  if (!isOpen) return null;

  return (
    <div style={{
      position: 'fixed',
      top: 0,
      left: 0,
      right: 0,
      bottom: 0,
      backgroundColor: 'rgba(0, 0, 0, 0.5)',
      display: 'flex',
      alignItems: 'center',
      justifyContent: 'center',
      zIndex: 1000
    }}>
      <div style={{
        backgroundColor: 'white',
        padding: '20px',
        borderRadius: '8px',
        maxWidth: '80%',
        maxHeight: '80%',
        overflow: 'auto',
        position: 'relative'
      }}>
        <button
          onClick={onClose}
          style={{
            position: 'absolute',
            top: '10px',
            right: '10px',
            background: 'none',
            border: 'none',
            fontSize: '20px',
            cursor: 'pointer',
            color: '#666'
          }}
        >
          ×
        </button>
        <h3 style={{ marginTop: 0, marginBottom: '16px' }}>
          {competency} - Level {score} Evidences
        </h3>
        
        {allLevelEvidences.length === 0 ? (
          <p style={{ color: '#666', fontStyle: 'italic' }}>No evidences found</p>
        ) : (
          <div>
            {allLevelEvidences.map((levelData, index) => (
              <div key={index} style={{
                marginBottom: '20px',
                padding: '12px',
                border: '1px solid #ddd',
                borderRadius: '4px',
                backgroundColor: levelData.achieved ? '#f8f9fa' : '#fff'
              }}>
                <h4 style={{
                  margin: '0 0 8px 0',
                  color: levelData.achieved ? '#28a745' : '#666',
                  fontSize: '14px'
                }}>
                  Level {levelData.order}: {levelData.level}
                  {levelData.achieved && ' ✓'}
                </h4>
                {levelData.evidences && levelData.evidences.length > 0 ? (
                  <ul style={{ margin: '0', paddingLeft: '20px' }}>
                    {levelData.evidences.map((evidence, evidenceIndex) => (
                      <li key={evidenceIndex} style={{
                        marginBottom: '4px',
                        fontSize: '13px',
                        lineHeight: '1.4'
                      }}>
                        "{evidence}"
                      </li>
                    ))}
                  </ul>
                ) : (
                  <p style={{
                    margin: '0',
                    color: '#999',
                    fontSize: '13px',
                    fontStyle: 'italic'
                  }}>
                    No evidences found for this level
                  </p>
                )}
              </div>
            ))}
          </div>
        )}
      </div>
    </div>
  );
};

// Individual row component for detailed results
const DetailedResultRow = ({ result, openInputModal, openEvidenceModal }) => {
  return (
    <tr style={{ backgroundColor: '#f8f9fa' }}>
      <td colSpan="8" style={{ padding: '16px' }}>
        <div style={{ display: 'grid', gap: '16px' }}>
          <div>
            <h4 style={{ margin: '0 0 8px 0', color: '#333' }}>Row {result.rowIndex} Details</h4>
            <button
              onClick={() => openInputModal(result.inputText)}
              style={{
                padding: '6px 12px',
                backgroundColor: '#007bff',
                color: 'white',
                border: 'none',
                borderRadius: '4px',
                cursor: 'pointer',
                fontSize: '12px'
              }}
            >
              View Input Text
            </button>
          </div>

          <div style={{ display: 'grid', gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))', gap: '16px' }}>
            {/* Innovation */}
            <div style={{ padding: '12px', border: '1px solid #ddd', borderRadius: '4px', backgroundColor: 'white' }}>
              <h5 style={{ margin: '0 0 8px 0', color: '#333' }}>Innovation</h5>
              <div style={{ fontSize: '12px', color: '#666' }}>
                Expected: <strong>{result.expectedInnovation}</strong><br />
                Actual: 
                <button
                  onClick={() => openEvidenceModal('Innovation', result.actualInnovation, result.geminiResponse)}
                  style={{
                    background: 'none',
                    border: 'none',
                    color: '#007bff',
                    textDecoration: 'underline',
                    cursor: 'pointer',
                    padding: '0',
                    margin: '0 0 0 4px',
                    fontSize: '12px',
                    fontWeight: 'bold'
                  }}
                >
                  {result.actualInnovation}
                </button><br />
                Difference: <span style={{ color: getDifferenceColor(result.innovationDiff), fontWeight: 'bold' }}>
                  {getDifferenceText(result.innovationDiff)}
                </span>
              </div>
            </div>

            {/* Accountability */}
            <div style={{ padding: '12px', border: '1px solid #ddd', borderRadius: '4px', backgroundColor: 'white' }}>
              <h5 style={{ margin: '0 0 8px 0', color: '#333' }}>Accountability</h5>
              <div style={{ fontSize: '12px', color: '#666' }}>
                Expected: <strong>{result.expectedAccountability}</strong><br />
                Actual: 
                <button
                  onClick={() => openEvidenceModal('Accountability', result.actualAccountability, result.geminiResponse)}
                  style={{
                    background: 'none',
                    border: 'none',
                    color: '#007bff',
                    textDecoration: 'underline',
                    cursor: 'pointer',
                    padding: '0',
                    margin: '0 0 0 4px',
                    fontSize: '12px',
                    fontWeight: 'bold'
                  }}
                >
                  {result.actualAccountability}
                </button><br />
                Difference: <span style={{ color: getDifferenceColor(result.accountabilityDiff), fontWeight: 'bold' }}>
                  {getDifferenceText(result.accountabilityDiff)}
                </span>
              </div>
            </div>

            {/* Leadership */}
            <div style={{ padding: '12px', border: '1px solid #ddd', borderRadius: '4px', backgroundColor: 'white' }}>
              <h5 style={{ margin: '0 0 8px 0', color: '#333' }}>Leadership</h5>
              <div style={{ fontSize: '12px', color: '#666' }}>
                Expected: <strong>{result.expectedLeadership}</strong><br />
                Actual: 
                <button
                  onClick={() => openEvidenceModal('Leadership', result.actualLeadership, result.geminiResponse)}
                  style={{
                    background: 'none',
                    border: 'none',
                    color: '#007bff',
                    textDecoration: 'underline',
                    cursor: 'pointer',
                    padding: '0',
                    margin: '0 0 0 4px',
                    fontSize: '12px',
                    fontWeight: 'bold'
                  }}
                >
                  {result.actualLeadership}
                </button><br />
                Difference: <span style={{ color: getDifferenceColor(result.leadershipDiff), fontWeight: 'bold' }}>
                  {getDifferenceText(result.leadershipDiff)}
                </span>
              </div>
            </div>

            {/* Problem Solving */}
            <div style={{ padding: '12px', border: '1px solid #ddd', borderRadius: '4px', backgroundColor: 'white' }}>
              <h5 style={{ margin: '0 0 8px 0', color: '#333' }}>Problem Solving</h5>
              <div style={{ fontSize: '12px', color: '#666' }}>
                Expected: <strong>{result.expectedProblemSolving}</strong><br />
                Actual: 
                <button
                  onClick={() => openEvidenceModal('Problem Solving', result.actualProblemSolving, result.geminiResponse)}
                  style={{
                    background: 'none',
                    border: 'none',
                    color: '#007bff',
                    textDecoration: 'underline',
                    cursor: 'pointer',
                    padding: '0',
                    margin: '0 0 0 4px',
                    fontSize: '12px',
                    fontWeight: 'bold'
                  }}
                >
                  {result.actualProblemSolving}
                </button><br />
                Difference: <span style={{ color: getDifferenceColor(result.problemSolvingDiff), fontWeight: 'bold' }}>
                  {getDifferenceText(result.problemSolvingDiff)}
                </span>
              </div>
            </div>
          </div>

          {result.error && (
            <div style={{
              padding: '12px',
              backgroundColor: '#f8d7da',
              border: '1px solid #f5c6cb',
              borderRadius: '4px',
              color: '#721c24'
            }}>
              <strong>Error:</strong> {result.error}
            </div>
          )}
        </div>
      </td>
    </tr>
  );
};

// Main component
const EssayResultsTable = ({ evaluations }) => {
  const [expandedRow, setExpandedRow] = useState(null);
  const [inputModalState, setInputModalState] = useState({
    isOpen: false,
    inputText: ''
  });
  const [evidenceModalState, setEvidenceModalState] = useState({
    isOpen: false,
    competency: '',
    score: 0,
    allLevelEvidences: []
  });

  const toggleRow = (id) => {
    setExpandedRow(expandedRow === id ? null : id);
  };

  const openInputModal = (inputText) => {
    setInputModalState({
      isOpen: true,
      inputText
    });
  };

  const closeInputModal = () => {
    setInputModalState({
      isOpen: false,
      inputText: ''
    });
  };

  const openEvidenceModal = (competency, score, geminiResponse) => {
    let allLevelEvidences = [];

    if (geminiResponse && geminiResponse.results) {
      const competencyData = geminiResponse.results.find(comp =>
        comp.competency === competency
      );

      if (competencyData && competencyData.levels) {
        // Get evidences from ALL levels, sorted by order
        allLevelEvidences = competencyData.levels
          .sort((a, b) => a.order - b.order)
          .map(level => ({
            level: level.level,
            order: level.order,
            evidences: level.evidences || [],
            achieved: level.check === true && level.order <= score
          }));
      }
    }

    setEvidenceModalState({
      isOpen: true,
      competency,
      score,
      allLevelEvidences
    });
  };

  const closeEvidenceModal = () => {
    setEvidenceModalState({
      isOpen: false,
      competency: '',
      score: 0,
      allLevelEvidences: []
    });
  };

  if (!evaluations || evaluations.length === 0) {
    return (
      <div style={{
        border: '1px solid #ddd',
        borderRadius: '8px',
        padding: '20px',
        textAlign: 'center',
        color: '#666',
        backgroundColor: 'white'
      }}>
        No Essay evaluations found. Run an evaluation to see results here.
      </div>
    );
  }

  return (
    <div style={{
      border: '1px solid #ddd',
      borderRadius: '8px',
      backgroundColor: 'white',
      overflow: 'hidden'
    }}>
      <div style={{
        padding: '16px',
        borderBottom: '1px solid #ddd',
        backgroundColor: '#f8f9fa'
      }}>
        <h3 style={{ margin: 0 }}>Essay Scoring Evaluation Results</h3>
      </div>

      <InputModal
        isOpen={inputModalState.isOpen}
        inputText={inputModalState.inputText}
        onClose={closeInputModal}
      />

      <EvidenceModal
        isOpen={evidenceModalState.isOpen}
        competency={evidenceModalState.competency}
        score={evidenceModalState.score}
        allLevelEvidences={evidenceModalState.allLevelEvidences}
        onClose={closeEvidenceModal}
      />

      <div style={{ overflowX: 'auto' }}>
        <table style={{ width: '100%', borderCollapse: 'collapse' }}>
          <thead>
            <tr style={{ backgroundColor: '#f8f9fa' }}>
              <th style={{ padding: '12px', textAlign: 'left', borderBottom: '1px solid #ddd' }}>
                Timestamp
              </th>
              <th style={{ padding: '12px', textAlign: 'left', borderBottom: '1px solid #ddd' }}>
                Dataset
              </th>
              <th style={{ padding: '12px', textAlign: 'center', borderBottom: '1px solid #ddd' }}>
                Prompt Version
              </th>
              <th style={{ padding: '12px', textAlign: 'center', borderBottom: '1px solid #ddd' }}>
                Status
              </th>
              <th style={{ padding: '12px', textAlign: 'center', borderBottom: '1px solid #ddd' }}>
                Innovation Accuracy
              </th>
              <th style={{ padding: '12px', textAlign: 'center', borderBottom: '1px solid #ddd' }}>
                Accountability Accuracy
              </th>
              <th style={{ padding: '12px', textAlign: 'center', borderBottom: '1px solid #ddd' }}>
                Leadership Accuracy
              </th>
              <th style={{ padding: '12px', textAlign: 'center', borderBottom: '1px solid #ddd' }}>
                Problem Solving Accuracy
              </th>
              <th style={{ padding: '12px', textAlign: 'center', borderBottom: '1px solid #ddd' }}>
                Rows Processed
              </th>
              <th style={{ padding: '12px', textAlign: 'center', borderBottom: '1px solid #ddd' }}>
                Actions
              </th>
            </tr>
          </thead>
          <tbody>
            {evaluations.map((evaluation) => (
              <React.Fragment key={evaluation.id}>
                <tr style={{
                  borderBottom: '1px solid #eee',
                  backgroundColor: expandedRow === evaluation.id ? '#f8f9fa' : 'white'
                }}>
                  <td style={{ padding: '12px', verticalAlign: 'top' }}>
                    {new Date(evaluation.timestamp).toLocaleString()}
                  </td>
                  <td style={{ padding: '12px', verticalAlign: 'top' }}>
                    {evaluation.dataset_name || 'Unknown Dataset'}
                  </td>
                  <td style={{ padding: '12px', textAlign: 'center', verticalAlign: 'top' }}>
                    {evaluation.prompt_version || 'N/A'}
                  </td>
                  <td style={{ padding: '12px', textAlign: 'center', verticalAlign: 'top' }}>
                    <span style={{
                      padding: '4px 8px',
                      borderRadius: '4px',
                      fontSize: '12px',
                      fontWeight: 'bold',
                      backgroundColor: evaluation.status === 'completed' ? '#d4edda' :
                                     evaluation.status === 'error' ? '#f8d7da' : '#fff3cd',
                      color: evaluation.status === 'completed' ? '#155724' :
                             evaluation.status === 'error' ? '#721c24' : '#856404'
                    }}>
                      {evaluation.status}
                    </span>
                  </td>
                  <td style={{ padding: '12px', textAlign: 'center', verticalAlign: 'top' }}>
                    {evaluation.innovation_accuracy !== null
                      ? `${(evaluation.innovation_accuracy * 100).toFixed(1)}%`
                      : 'N/A'
                    }
                  </td>
                  <td style={{ padding: '12px', textAlign: 'center', verticalAlign: 'top' }}>
                    {evaluation.accountability_accuracy !== null
                      ? `${(evaluation.accountability_accuracy * 100).toFixed(1)}%`
                      : 'N/A'
                    }
                  </td>
                  <td style={{ padding: '12px', textAlign: 'center', verticalAlign: 'top' }}>
                    {evaluation.leadership_accuracy !== null
                      ? `${(evaluation.leadership_accuracy * 100).toFixed(1)}%`
                      : 'N/A'
                    }
                  </td>
                  <td style={{ padding: '12px', textAlign: 'center', verticalAlign: 'top' }}>
                    {evaluation.problem_solving_accuracy !== null
                      ? `${(evaluation.problem_solving_accuracy * 100).toFixed(1)}%`
                      : 'N/A'
                    }
                  </td>
                  <td style={{ padding: '12px', textAlign: 'center', verticalAlign: 'top' }}>
                    {evaluation.total_rows_processed || 0}
                  </td>
                  <td style={{ padding: '12px', textAlign: 'center', verticalAlign: 'top' }}>
                    {evaluation.status === 'completed' && evaluation.details && evaluation.details.results && (
                      <button
                        onClick={() => toggleRow(evaluation.id)}
                        style={{
                          padding: '6px 12px',
                          backgroundColor: expandedRow === evaluation.id ? '#6c757d' : '#007bff',
                          color: 'white',
                          border: 'none',
                          borderRadius: '4px',
                          cursor: 'pointer',
                          fontSize: '12px'
                        }}
                      >
                        {expandedRow === evaluation.id ? 'Hide Details' : 'Show Details'}
                      </button>
                    )}
                  </td>
                </tr>

                {expandedRow === evaluation.id && evaluation.details && evaluation.details.results && (
                  evaluation.details.results.map((result, index) => (
                    <DetailedResultRow
                      key={index}
                      result={result}
                      openInputModal={openInputModal}
                      openEvidenceModal={openEvidenceModal}
                    />
                  ))
                )}
              </React.Fragment>
            ))}
          </tbody>
        </table>
      </div>
    </div>
  );
};

export default EssayResultsTable;
